defmodule MqttableWeb.TwoTabTemplateHelperComponent do
  @moduledoc """
  Two-tab template helper component with optimized space usage.

  This component provides template functionality with:
  - Two main tabs: Data Generation and Data Processing
  - Always-expanded sub-groups for full visibility
  - Compact vertical layout for space efficiency
  - Function insertion and live preview
  - Example templates
  """

  use MqttableWeb, :live_component
  alias Mqttable.Templating.Engine
  alias MqttableWeb.Live.Components.ArrayDataExample
  require Logger

  @impl true
  def mount(socket) do
    socket =
      socket
      |> assign_new(:active_tab, fn -> "data_generation" end)
      |> assign_new(:preview_result, fn -> {:ok, ""} end)

    {:ok, socket}
  end

  @impl true
  def update(assigns, socket) do
    # Get payload and format from assigns
    payload = Map.get(assigns, :payload, "")
    _payload_format = Map.get(assigns, :payload_format, "text")
    active_broker_name = Map.get(assigns, :active_broker_name)

    # Update preview
    preview_result = generate_preview(payload, active_broker_name)

    socket =
      socket
      |> assign(assigns)
      |> assign(:preview_result, preview_result)

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="h-full flex flex-col">
      <!-- Template Helper Header -->
      <div class="flex-shrink-0 mb-4">
        <h4 class="text-md font-semibold flex items-center mb-3">
          <.icon name="hero-sparkles" class="size-4 mr-2" /> Template Helper
        </h4>
        
    <!-- Two-Tab Navigation -->
        <div class="tabs tabs-boxed w-full">
          <button
            class={["tab", if(@active_tab == "data_generation", do: "tab-active", else: "")]}
            phx-click="switch_tab"
            phx-value-tab="data_generation"
            phx-target={@myself}
          >
            📊 Data Generation
          </button>
          <button
            class={["tab", if(@active_tab == "data_processing", do: "tab-active", else: "")]}
            phx-click="switch_tab"
            phx-value-tab="data_processing"
            phx-target={@myself}
          >
            🔧 Data Processing
          </button>
        </div>
      </div>
      
    <!-- Tab Content -->
      <div class="flex-1 overflow-y-auto">
        <%= if @active_tab == "data_generation" do %>
          <div class="space-y-2">
            <%= for group_info <- get_data_generation_groups() do %>
              <div class="bg-base-100 border border-base-300 rounded-lg">
                <!-- Always visible group header -->
                <div class="px-3 py-2 bg-base-200 rounded-t-lg border-b border-base-300">
                  <div class="text-xs font-semibold text-base-content">
                    {group_info.icon} {group_info.title} ({length(group_info.functions)})
                  </div>
                </div>
                <!-- Always visible function buttons -->
                <div class="p-2">
                  <div class="flex flex-wrap gap-1">
                    <%= for function_info <- group_info.functions do %>
                      <div class="tooltip tooltip-top" data-tip={function_info.tooltip}>
                        <button
                          type="button"
                          class="btn btn-xs btn-outline"
                          phx-click="insert_template"
                          phx-value-template={"{{ #{function_info.name} }}"}
                          phx-target={@myself}
                        >
                          {function_info.icon} {function_info.display_name}
                        </button>
                      </div>
                    <% end %>
                  </div>
                  <%= if Map.has_key?(group_info, :comprehensive_example) do %>
                    <div class="mt-2 pt-2 border-t border-base-300">
                      <button
                        type="button"
                        class="btn btn-xs btn-primary"
                        phx-click="insert_template"
                        phx-value-template={group_info.comprehensive_example.()}
                        phx-target={@myself}
                      >
                        📋 Complete {group_info.title} Example
                      </button>
                    </div>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <div class="space-y-2">
            <%= for group_info <- get_data_processing_groups() do %>
              <div class="bg-base-100 border border-base-300 rounded-lg">
                <!-- Always visible group header -->
                <div class="px-3 py-2 bg-base-200 rounded-t-lg border-b border-base-300">
                  <div class="text-xs font-semibold text-base-content">
                    {group_info.icon} {group_info.title} ({length(group_info.functions)})
                  </div>
                </div>
                <!-- Always visible function buttons -->
                <div class="p-2">
                  <div class="flex flex-wrap gap-1">
                    <%= for function_info <- group_info.functions do %>
                      <div class="tooltip tooltip-top" data-tip={function_info.tooltip}>
                        <button
                          type="button"
                          class="btn btn-xs btn-outline"
                          phx-click="insert_template"
                          phx-value-template={"{{ #{function_info.name} }}"}
                          phx-target={@myself}
                        >
                          {function_info.icon} {function_info.display_name}
                        </button>
                      </div>
                    <% end %>
                  </div>
                  <%= if Map.has_key?(group_info, :comprehensive_example) do %>
                    <div class="mt-2 pt-2 border-t border-base-300">
                      <button
                        type="button"
                        class="btn btn-xs btn-primary"
                        phx-click="insert_template"
                        phx-value-template={group_info.comprehensive_example.()}
                        phx-target={@myself}
                      >
                        📋 Complete {group_info.title} Example
                      </button>
                    </div>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        <% end %>
        
    <!-- Example Templates -->
        <div class="mt-6 pt-4 border-t border-base-300">
          <div class="text-sm font-medium mb-2">Example Templates:</div>
          
    <!-- Basic Examples - 2 per row -->
          <div class="grid grid-cols-2 gap-1 mb-2">
            <button
              type="button"
              class="btn btn-xs btn-ghost text-left justify-start text-xs"
              phx-click="insert_template"
              phx-value-template={sensor_data_example()}
              phx-target={@myself}
            >
              📊 IoT Sensor
            </button>
            <button
              type="button"
              class="btn btn-xs btn-ghost text-left justify-start text-xs"
              phx-click="insert_template"
              phx-value-template={user_profile_example()}
              phx-target={@myself}
            >
              👤 User Profile
            </button>
          </div>

          <div class="grid grid-cols-2 gap-1 mb-2">
            <button
              type="button"
              class="btn btn-xs btn-ghost text-left justify-start text-xs"
              phx-click="insert_template"
              phx-value-template={device_status_example()}
              phx-target={@myself}
            >
              🔧 Device Status
            </button>
            <button
              type="button"
              class="btn btn-xs btn-ghost text-left justify-start text-xs"
              phx-click="insert_template"
              phx-value-template={ArrayDataExample.for_loop_example()}
              phx-target={@myself}
            >
              🔄 For Loop
            </button>
          </div>
          
    <!-- Advanced Examples - 2 per row -->
          <div class="text-xs font-medium mb-1 text-base-content/70">Advanced Liquid Syntax:</div>
          <div class="grid grid-cols-2 gap-1 mb-2">
            <button
              type="button"
              class="btn btn-xs btn-ghost text-left justify-start text-xs"
              phx-click="insert_template"
              phx-value-template={ArrayDataExample.for_loop_array_example()}
              phx-target={@myself}
            >
              🔄 Array Loop
            </button>
            <button
              type="button"
              class="btn btn-xs btn-ghost text-left justify-start text-xs"
              phx-click="insert_template"
              phx-value-template={ArrayDataExample.nested_for_loop_example()}
              phx-target={@myself}
            >
              🔄 Nested Loop
            </button>
          </div>

          <div class="grid grid-cols-1 gap-1 mb-2">
            <button
              type="button"
              class="btn btn-xs btn-ghost text-left justify-start text-xs"
              phx-click="insert_template"
              phx-value-template={ArrayDataExample.conditional_for_loop_example()}
              phx-target={@myself}
            >
              🔄 Conditional Loop
            </button>
          </div>
          
    <!-- Liquid Documentation Link -->
          <div class="text-xs text-base-content/60 mt-2 pt-2 border-t border-base-300">
            💡 Supports full
            <a href="https://shopify.github.io/liquid/" target="_blank" class="link link-primary">
              Liquid syntax
            </a>
          </div>
        </div>
      </div>
      
    <!-- Live Preview -->
      <div class="flex-shrink-0 mt-4 pt-4 border-t border-base-300">
        <div class="text-sm font-medium mb-2">Live Preview:</div>
        <div class="bg-base-200 rounded p-2 text-xs font-mono max-h-32 overflow-y-auto">
          <%= case @preview_result do %>
            <% {:ok, result} -> %>
              <pre class="whitespace-pre-wrap text-success"><%= result %></pre>
            <% {:error, error} -> %>
              <pre class="whitespace-pre-wrap text-error"><%= error %></pre>
          <% end %>
        </div>
      </div>
    </div>
    """
  end

  # Event Handlers

  @impl true
  def handle_event("switch_tab", %{"tab" => tab}, socket) do
    {:noreply, assign(socket, :active_tab, tab)}
  end

  @impl true
  def handle_event("insert_template", %{"template" => template}, socket) do
    # Push event to JavaScript to insert at cursor position in the target textarea
    # Also notify parent about the payload change
    new_payload = socket.assigns.payload <> template
    send(self(), {:payload_editor_changed, new_payload, socket.assigns.payload_format})

    {:noreply,
     push_event(socket, "insert_at_cursor", %{
       target_id: socket.assigns.target_textarea_id,
       text: template
     })}
  end

  # Helper Functions

  defp generate_preview(payload, active_broker_name) do
    if String.contains?(payload, "{{") || String.contains?(payload, "{%") do
      # Get broker variables if broker_name is provided
      variables = get_broker_variables(active_broker_name)

      case Engine.render(payload, %{}, variables) do
        {:ok, result} -> {:ok, result}
        {:error, error} -> {:error, inspect(error)}
      end
    else
      {:ok, payload}
    end
  end

  # Data Generation Groups - Returns ordered list for custom prioritization
  defp get_data_generation_groups do
    [
      # Random & Utility (frequently used)
      %{
        key: "random_utility",
        title: "Random & Utility",
        icon: "🎲",
        comprehensive_example: &random_utility_comprehensive_example/0,
        functions: [
          %{
            name: "uuid",
            display_name: "UUID",
            icon: "🆔",
            description: "Random UUID",
            tooltip: "{{ uuid }}"
          },
          %{
            name: "random_int",
            display_name: "Random Int",
            icon: "🎲",
            description: "Random integer",
            tooltip: "{{ 0 | random_int: 100 }}"
          },
          %{
            name: "random_float",
            display_name: "Random Float",
            icon: "🔀",
            description: "Random float",
            tooltip: "{{ 0.0 | random_float: 1.0, 2 }}"
          },
          %{
            name: "random_string",
            display_name: "Random String",
            icon: "🧵",
            description: "Random string",
            tooltip: "{{ 8 | random_string: \"alphanumeric\" }}\nalphanumeric|alpha|numeric|hex"
          },
          %{
            name: "random_choice",
            display_name: "Random Choice",
            icon: "🎡",
            description: "Random choice",
            tooltip: "{{ random_choice([\"red\", \"green\", \"blue\"]) }}"
          },
          %{
            name: "random_bool",
            display_name: "Random Bool",
            icon: "🎲",
            description: "Random boolean",
            tooltip: "{{ random_bool }}"
          },
          %{
            name: "random_hex",
            display_name: "Random Hex",
            icon: "🔯",
            description: "Random hex string",
            tooltip: "{{ 8 | random_hex }}"
          },
          %{
            name: "color_name",
            display_name: "Color",
            icon: "🎨",
            description: "Random color name",
            tooltip: "{{ color_name }}"
          },
          %{
            name: "rgb_hex",
            display_name: "RGB Hex",
            icon: "🎨",
            description: "Random RGB hex color",
            tooltip: "{{ rgb_hex }}"
          }
        ]
      },
      # Time & Date (frequently used)
      %{
        key: "time_date",
        title: "Time & Date",
        icon: "⏰",
        comprehensive_example: &time_date_comprehensive_example/0,
        functions: [
          %{
            name: "now",
            display_name: "Now",
            icon: "🕰️",
            description: "Current time",
            tooltip: "{{ \"iso8601\" | now: \"utc\" }}"
          },
          %{
            name: "iso8601",
            display_name: "ISO8601",
            icon: "⏰",
            description: "ISO8601 timestamp",
            tooltip: "{{ iso8601 }}"
          },
          %{
            name: "timestamp",
            display_name: "Timestamp",
            icon: "⏱️",
            description: "Current timestamp",
            tooltip: "{{ 3600 | timestamp: \"unix\" }}"
          },
          %{
            name: "unix_timestamp",
            display_name: "Unix Time",
            icon: "⏱️",
            description: "Unix timestamp",
            tooltip: "{{ unix_timestamp }}"
          },
          %{
            name: "past_date",
            display_name: "Past Date",
            icon: "⏱️",
            description: "Random past date",
            tooltip: "{{ 365 | past_date }}"
          },
          %{
            name: "future_date",
            display_name: "Future Date",
            icon: "⏱️",
            description: "Random future date",
            tooltip: "{{ 365 | future_date }}"
          }
        ]
      },
      %{
        key: "iot_sensors",
        title: "IoT & Sensors",
        icon: "🌡️",
        comprehensive_example: &iot_sensors_comprehensive_example/0,
        functions: [
          %{
            name: "temperature",
            display_name: "Temperature",
            icon: "🌡️",
            description: "Random temperature value",
            tooltip: "{{ 15 | temperature: 30 }}"
          },
          %{
            name: "humidity",
            display_name: "Humidity",
            icon: "💧",
            description: "Random humidity percentage",
            tooltip: "{{ 0 | humidity: 100 }}"
          },
          %{
            name: "pressure",
            display_name: "Pressure",
            icon: "📊",
            description: "Random pressure value",
            tooltip: "{{ 100 | pressure: 1024 }}"
          },
          %{
            name: "battery_level",
            display_name: "Battery",
            icon: "🔋",
            description: "Random battery level",
            tooltip: "{{ battery_level }}"
          },
          %{
            name: "device_id",
            display_name: "Device ID",
            icon: "📱",
            description: "Random device identifier",
            tooltip: "{{ \"dev\" |device_id: 2 }}"
          },
          %{
            name: "device_status",
            display_name: "Status",
            icon: "📊",
            description: "Random device status",
            tooltip: "{{ device_status }}"
          },
          %{
            name: "signal_strength",
            display_name: "Signal",
            icon: "📶",
            description: "Random signal strength",
            tooltip: "{{ -90 | signal_strength: -30 }}"
          },
          %{
            name: "light_level",
            display_name: "Light",
            icon: "💡",
            description: "Random light level",
            tooltip: "{{ 0 | light_level: 1000 }}"
          },
          %{
            name: "air_quality",
            display_name: "Air Quality",
            icon: "🌬️",
            description: "Random air quality index",
            tooltip: "{{ 0 | air_quality: 300 }}"
          },
          %{
            name: "firmware_version",
            display_name: "Firmware",
            icon: "💾",
            description: "Firmware version",
            tooltip: "{{ firmware_version }}"
          },
          %{
            name: "uptime",
            display_name: "Uptime",
            icon: "⏱️",
            description: "Device uptime",
            tooltip: "{{ 1 | uptime }}"
          }
        ]
      },
      # Person Data
      %{
        key: "person_data",
        title: "Person Data",
        icon: "👤",
        comprehensive_example: &person_data_comprehensive_example/0,
        functions: [
          %{
            name: "name",
            display_name: "Name",
            icon: "👤",
            description: "Random full name",
            tooltip: "{{ name }}"
          },
          %{
            name: "first_name",
            display_name: "First Name",
            icon: "👤",
            description: "Random first name",
            tooltip: "{{ first_name }}"
          },
          %{
            name: "last_name",
            display_name: "Last Name",
            icon: "👤",
            description: "Random last name",
            tooltip: "{{ last_name }}"
          },
          %{
            name: "email",
            display_name: "Email",
            icon: "📧",
            description: "Random email address",
            tooltip: "{{ email }}"
          },
          %{
            name: "username",
            display_name: "Username",
            icon: "👤",
            description: "Random username",
            tooltip: "{{ username }}"
          },
          %{
            name: "title",
            display_name: "Title",
            icon: "🎓",
            description: "Random title",
            tooltip: "{{ title }}"
          },
          %{
            name: "date_of_birth",
            display_name: "Birth Date",
            icon: "🎂",
            description: "Random birth date",
            tooltip: "{{ date_of_birth }}"
          }
        ]
      },
      # Address & Location
      %{
        key: "location_data",
        title: "Address & Location",
        icon: "📍",
        comprehensive_example: &address_location_comprehensive_example/0,
        functions: [
          %{
            name: "address",
            display_name: "Address",
            icon: "🏠",
            description: "Random street address",
            tooltip: "{{ address }}"
          },
          %{
            name: "city",
            display_name: "City",
            icon: "🏙️",
            description: "Random city name",
            tooltip: "{{ city }}"
          },
          %{
            name: "country",
            display_name: "Country",
            icon: "🌍",
            description: "Random country name",
            tooltip: "{{ country }}"
          },
          %{
            name: "country_code",
            display_name: "Country Code",
            icon: "🌍",
            description: "Random country code",
            tooltip: "{{ country_code }}"
          },
          %{
            name: "state",
            display_name: "State",
            icon: "🗺️",
            description: "Random state name",
            tooltip: "{{ state }}"
          },
          %{
            name: "postcode",
            display_name: "Postcode",
            icon: "📮",
            description: "Random postal code",
            tooltip: "{{ postcode }}"
          },
          %{
            name: "latitude",
            display_name: "Latitude",
            icon: "🗺️",
            description: "Random latitude",
            tooltip: "{{ latitude }}"
          },
          %{
            name: "longitude",
            display_name: "Longitude",
            icon: "🗺️",
            description: "Random longitude",
            tooltip: "{{ longitude }}"
          },
          %{
            name: "timezone",
            display_name: "Timezone",
            icon: "🕐",
            description: "Random timezone",
            tooltip: "{{ timezone }}"
          },
          %{
            name: "geohash",
            display_name: "Geohash",
            icon: "🗺️",
            description: "Random geohash location",
            tooltip: "{{ geohash }}"
          }
        ]
      },
      # Internet & Tech
      %{
        key: "internet_data",
        title: "Internet & Tech",
        icon: "🌐",
        comprehensive_example: &internet_tech_comprehensive_example/0,
        functions: [
          %{
            name: "domain",
            display_name: "Domain",
            icon: "🌐",
            description: "Random domain name",
            tooltip: "{{ domain }}"
          },
          %{
            name: "url",
            display_name: "URL",
            icon: "🔗",
            description: "Random URL",
            tooltip: "{{ url }}"
          },
          %{
            name: "ipv4",
            display_name: "IPv4",
            icon: "🌐",
            description: "Random IPv4 address",
            tooltip: "{{ ipv4 }}"
          },
          %{
            name: "ipv6",
            display_name: "IPv6",
            icon: "🌐",
            description: "Random IPv6 address",
            tooltip: "{{ ipv6 }}"
          },
          %{
            name: "mac_address",
            display_name: "MAC Address",
            icon: "🔌",
            description: "Random MAC address",
            tooltip: "{{ mac_address }}"
          },
          %{
            name: "user_agent",
            display_name: "User Agent",
            icon: "🌐",
            description: "Random browser user agent",
            tooltip: "{{ user_agent }}"
          }
        ]
      },
      # Company & Business
      %{
        key: "company_data",
        title: "Company & Business",
        icon: "🏢",
        comprehensive_example: &company_business_comprehensive_example/0,
        functions: [
          %{
            name: "company",
            display_name: "Company",
            icon: "🏢",
            description: "Random company name",
            tooltip: "{{ company }}"
          },
          %{
            name: "company_suffix",
            display_name: "Company Suffix",
            icon: "🏢",
            description: "Random company suffix",
            tooltip: "{{ company_suffix }}"
          },
          %{
            name: "buzzword",
            display_name: "Buzzword",
            icon: "💼",
            description: "Random business buzzword",
            tooltip: "{{ buzzword }}"
          },
          %{
            name: "catch_phrase",
            display_name: "Catch Phrase",
            icon: "💼",
            description: "Random catch phrase",
            tooltip: "{{ catch_phrase }}"
          },
          %{
            name: "department",
            display_name: "Department",
            icon: "🏢",
            description: "Random department name",
            tooltip: "{{ department }}"
          }
        ]
      },
      # Commerce & Products
      %{
        key: "commerce_data",
        title: "Commerce & Products",
        icon: "🛒",
        comprehensive_example: &commerce_products_comprehensive_example/0,
        functions: [
          %{
            name: "product_name",
            display_name: "Product",
            icon: "📦",
            description: "Random product name",
            tooltip: "{{ product_name }}"
          },
          %{
            name: "price",
            display_name: "Price",
            icon: "💰",
            description: "Random price",
            tooltip: "{{ price }}"
          },
          %{
            name: "vehicle",
            display_name: "Vehicle",
            icon: "🚗",
            description: "Random vehicle",
            tooltip: "{{ vehicle }}"
          },
          %{
            name: "vehicle_make",
            display_name: "Car Make",
            icon: "🚗",
            description: "Random vehicle make",
            tooltip: "{{ vehicle_make }}"
          },
          %{
            name: "vehicle_model",
            display_name: "Car Model",
            icon: "🚗",
            description: "Random vehicle model",
            tooltip: "{{ vehicle_model }}"
          },
          %{
            name: "vin",
            display_name: "VIN",
            icon: "🚗",
            description: "Random VIN number",
            tooltip: "{{ vin }}"
          }
        ]
      },
      # Text & Content
      %{
        key: "text_content",
        title: "Text & Content",
        icon: "📝",
        comprehensive_example: &text_content_comprehensive_example/0,
        functions: [
          %{
            name: "sentence",
            display_name: "Sentence",
            icon: "📝",
            description: "Random sentence",
            tooltip: "{{ sentence }}"
          },
          %{
            name: "paragraph",
            display_name: "Paragraph",
            icon: "📄",
            description: "Random paragraph",
            tooltip: "{{ paragraph }}"
          },
          %{
            name: "word",
            display_name: "Word",
            icon: "🔤",
            description: "Random word",
            tooltip: "{{ word }}"
          },
          %{
            name: "words",
            display_name: "Words",
            icon: "🔤",
            description: "Random words",
            tooltip: "{{ 3 | words }}"
          },
          %{
            name: "markdown",
            display_name: "Markdown",
            icon: "📝",
            description: "Random markdown content",
            tooltip: "{{ markdown }}"
          },
          %{
            name: "markdown_headers",
            display_name: "MD Headers",
            icon: "📝",
            description: "Random markdown headers",
            tooltip: "{{ markdown_headers }}"
          },
          %{
            name: "markdown_emphasis",
            display_name: "MD Emphasis",
            icon: "📝",
            description: "Random markdown emphasis",
            tooltip: "{{ markdown_emphasis }}"
          },
          %{
            name: "markdown_inline_code",
            display_name: "MD Inline Code",
            icon: "📝",
            description: "Random markdown inline code",
            tooltip: "{{ markdown_inline_code }}"
          },
          %{
            name: "markdown_block_code",
            display_name: "MD Block Code",
            icon: "📝",
            description: "Random markdown code block",
            tooltip: "{{ markdown_block_code }}"
          },
          %{
            name: "markdown_ordered_list",
            display_name: "MD Ordered List",
            icon: "📝",
            description: "Random markdown ordered list",
            tooltip: "{{ markdown_ordered_list }}"
          },
          %{
            name: "markdown_unordered_list",
            display_name: "MD Unordered List",
            icon: "📝",
            description: "Random markdown unordered list",
            tooltip: "{{ markdown_unordered_list }}"
          },
          %{
            name: "markdown_table",
            display_name: "MD Table",
            icon: "📝",
            description: "Random markdown table",
            tooltip: "{{ markdown_table }}"
          }
        ]
      },
      # Financial & Currency
      %{
        key: "financial_data",
        title: "Financial & Currency",
        icon: "💰",
        comprehensive_example: &financial_currency_comprehensive_example/0,
        functions: [
          %{
            name: "currency_code",
            display_name: "Currency Code",
            icon: "💰",
            description: "Random currency code",
            tooltip: "{{ currency_code }}"
          },
          %{
            name: "currency_symbol",
            display_name: "Currency Symbol",
            icon: "💰",
            description: "Random currency symbol",
            tooltip: "{{ currency_symbol }}"
          },
          %{
            name: "iban",
            display_name: "IBAN",
            icon: "🏦",
            description: "Random IBAN",
            tooltip: "{{ iban }}"
          },
          %{
            name: "bitcoin_address",
            display_name: "Bitcoin Address",
            icon: "₿",
            description: "Random Bitcoin address",
            tooltip: "{{ bitcoin_address }}"
          },
          %{
            name: "ethereum_address",
            display_name: "Ethereum Address",
            icon: "Ξ",
            description: "Random Ethereum address",
            tooltip: "{{ ethereum_address }}"
          },
          %{
            name: "ethereum_signature",
            display_name: "Ethereum Signature",
            icon: "Ξ",
            description: "Random Ethereum signature",
            tooltip: "{{ ethereum_signature }}"
          }
        ]
      },
      # Food & Ingredients
      %{
        key: "food_data",
        title: "Food & Ingredients",
        icon: "🍽️",
        comprehensive_example: &food_ingredients_comprehensive_example/0,
        functions: [
          %{
            name: "dish",
            display_name: "Dish",
            icon: "🍽️",
            description: "Random dish name",
            tooltip: "{{ dish }}"
          },
          %{
            name: "ingredient",
            display_name: "Ingredient",
            icon: "🥕",
            description: "Random ingredient",
            tooltip: "{{ ingredient }}"
          },
          %{
            name: "spice",
            display_name: "Spice",
            icon: "🌶️",
            description: "Random spice",
            tooltip: "{{ spice }}"
          }
        ]
      },
      # Files & Media
      %{
        key: "file_data",
        title: "Files & Media",
        icon: "📁",
        comprehensive_example: &files_media_comprehensive_example/0,
        functions: [
          %{
            name: "filename",
            display_name: "Filename",
            icon: "📄",
            description: "Random filename",
            tooltip: "{{ filename }}"
          },
          %{
            name: "file_extension",
            display_name: "File Extension",
            icon: "📄",
            description: "Random file extension",
            tooltip: "{{ file_extension }}"
          },
          %{
            name: "mime_type",
            display_name: "MIME Type",
            icon: "📄",
            description: "Random MIME type",
            tooltip: "{{ mime_type }}"
          },
          %{
            name: "avatar_url",
            display_name: "Avatar URL",
            icon: "👤",
            description: "Random avatar URL",
            tooltip: "{{ avatar_url }}"
          },
          %{
            name: "image_url",
            display_name: "Image URL",
            icon: "🖼️",
            description: "Random image URL",
            tooltip: "{{ image_url }}"
          }
        ]
      }
    ]
  end

  # Data Processing Groups - Returns ordered list for custom prioritization
  defp get_data_processing_groups do
    [
      # Encoding & Decoding (most frequently used)
      %{
        key: "encoding",
        title: "Encoding & Decoding",
        icon: "🔐",
        comprehensive_example: &encoding_decoding_comprehensive_example/0,
        functions: [
          %{
            name: "base64_encode",
            display_name: "Base64 Encode",
            icon: "🔐",
            description: "Encode to Base64",
            tooltip: "{{ random_string | base64_encode }}"
          },
          %{
            name: "base64_decode",
            display_name: "Base64 Decode",
            icon: "🔓",
            description: "Decode from Base64",
            tooltip: "{{ \"NXY0YmVhamJ0dw==\" | base64_decode }}"
          },
          %{
            name: "json_encode",
            display_name: "JSON Encode",
            icon: "📝",
            description: "Encode to JSON",
            tooltip: "{{ {\"key\": \"value\"} | json_encode }}"
          },
          %{
            name: "json_decode",
            display_name: "JSON Decode",
            icon: "📖",
            description: "Decode from JSON",
            tooltip: "{{ \"{\\\"key\\\":\\\"value\\\"}\" | json_decode }}"
          },
          %{
            name: "url_encode",
            display_name: "URL Encode",
            icon: "🔗",
            description: "URL encode string",
            tooltip: "{{ \"hello world\" | url_encode }}"
          },
          %{
            name: "url_decode",
            display_name: "URL Decode",
            icon: "🔗",
            description: "URL decode string",
            tooltip: "{{ \"hello%20world\" | url_decode }}"
          },
          %{
            name: "hash",
            display_name: "Hash",
            icon: "🔒",
            description: "Generate hash",
            tooltip: "{{ random_string | hash: \"md5\" }}"
          }
        ]
      },
      # Math Functions
      %{
        key: "math_functions",
        title: "Math Functions",
        icon: "🧮",
        comprehensive_example: &math_functions_comprehensive_example/0,
        functions: [
          %{
            name: "round",
            display_name: "Round",
            icon: "🔢",
            description: "Round number",
            tooltip: "{{ 3.14159 | round: 2 }}"
          },
          %{
            name: "ceil",
            display_name: "Ceiling",
            icon: "⬆️",
            description: "Round up",
            tooltip: "{{ 3.14 | ceil }}"
          },
          %{
            name: "floor",
            display_name: "Floor",
            icon: "⬇️",
            description: "Round down",
            tooltip: "{{ 3.14 | floor }}"
          },
          %{
            name: "abs",
            display_name: "Absolute",
            icon: "🔢",
            description: "Absolute value",
            tooltip: "{{ -5 | abs }}"
          },
          %{
            name: "min",
            display_name: "Minimum",
            icon: "⬇️",
            description: "Minimum value",
            tooltip: "{{ 5 | min: 3 }}"
          },
          %{
            name: "max",
            display_name: "Maximum",
            icon: "⬆️",
            description: "Maximum value",
            tooltip: "{{ 5 | max: 3 }}"
          },
          %{
            name: "clamp",
            display_name: "Clamp",
            icon: "🔢",
            description: "Clamp value",
            tooltip: "{{ 10 | clamp: 1, 5 }}"
          }
        ]
      },
      # String Manipulation
      %{
        key: "string_functions",
        title: "String Manipulation",
        icon: "📝",
        comprehensive_example: &string_manipulation_comprehensive_example/0,
        functions: [
          %{
            name: "capitalize",
            display_name: "Capitalize",
            icon: "🔤",
            description: "Capitalize string",
            tooltip: "{{ \"hello\" | capitalize }}"
          },
          %{
            name: "uppercase",
            display_name: "Uppercase",
            icon: "🔤",
            description: "Convert to uppercase",
            tooltip: "{{ \"hello\" | uppercase }}"
          },
          %{
            name: "lowercase",
            display_name: "Lowercase",
            icon: "🔤",
            description: "Convert to lowercase",
            tooltip: "{{ \"HELLO\" | lowercase }}"
          },
          %{
            name: "truncate",
            display_name: "Truncate",
            icon: "✂️",
            description: "Truncate string",
            tooltip: "{{ \"hello world\" | truncate: 5 }}"
          },
          %{
            name: "pad_left",
            display_name: "Pad Left",
            icon: "⬅️",
            description: "Pad string left",
            tooltip: "{{ \"hello\" | pad_left: 10, \"0\" }}"
          },
          %{
            name: "pad_right",
            display_name: "Pad Right",
            icon: "➡️",
            description: "Pad string right",
            tooltip: "{{ \"hello\" | pad_right: 10, \"0\" }}"
          }
        ]
      },
      # Date Processing
      %{
        key: "date_functions",
        title: "Date Processing",
        icon: "📅",
        comprehensive_example: &date_processing_comprehensive_example/0,
        functions: [
          %{
            name: "date_add",
            display_name: "Date Add",
            icon: "➕",
            description: "Add to date",
            tooltip: "{{ 3600 | date_add }}"
          },
          %{
            name: "date_format",
            display_name: "Date Format",
            icon: "📅",
            description: "Format date",
            tooltip: "{{ date_format: \"%Y-%m-%d\" }}"
          },
          %{
            name: "timezone_convert",
            display_name: "Timezone Convert",
            icon: "🌍",
            description: "Convert timezone",
            tooltip: "{{ now | timezone_convert: \"America/New_York\" }}"
          }
        ]
      }
    ]
  end

  # Example Templates
  defp sensor_data_example do
    """
    {
      "device_id": "{{ device_id }}",
      "timestamp": "{{ iso8601 }}",
      "temperature": {{ temperature }},
      "humidity": {{ humidity }},
      "location": {
        "city": "{{ city }}",
        "country": "{{ country }}"
      }
    }
    """
  end

  defp user_profile_example do
    """
    {
      "user_id": "{{ uuid }}",
      "name": "{{ name }}",
      "email": "{{ email }}",
      "address": {
        "city": "{{ city }}",
        "country": "{{ country }}"
      }
    }
    """
  end

  defp device_status_example do
    """
    {
      "device": "{{ device_id }}",
      "status": "{{ device_status }}",
      "last_seen": "{{ iso8601 }}",
      "battery": {{ battery_level }}
    }
    """
  end

  # Comprehensive Examples for Each Category
  defp random_utility_comprehensive_example do
    """
    {
      "session_id": "{{ uuid }}",
      "basic_random": {
        "simple_int": {{ random_int }},
        "ranged_int": {{ 10 | random_int: 100 }},
        "simple_float": {{ random_float }},
        "precise_float": {{ 0.0 | random_float: 1.0, 3 }},
        "simple_bool": {{ random_bool }},
        "simple_hex": "{{ 6 | random_hex }}",
        "long_hex": "{{ 16 | random_hex }}"
      },
      "string_generation": {
        "default_string": "{{ random_string }}",
        "alpha_string": "{{ 10 | random_string: \"alpha\" }}",
        "numeric_string": "{{ 8 | random_string: \"numeric\" }}",
        "alphanumeric_string": "{{ 12 | random_string: \"alphanumeric\" }}",
        "hex_string": "{{ 16 | random_string: \"hex\" }}"
      },
      "choices_and_colors": {
        "status": "{{ random_choice([\"active\", \"inactive\", \"pending\", \"suspended\"]) }}",
        "priority": "{{ random_choice([\"low\", \"medium\", \"high\", \"critical\"]) }}",
        "color_name": "{{ color_name }}",
        "color_hex": "{{ rgb_hex }}"
      },
      "metadata": {
        "generated_at": "{{ iso8601 }}",
        "version": "1.0"
      }
    }
    """
  end

  defp time_date_comprehensive_example do
    """
    {
      "event_id": "{{ uuid }}",
      "timestamps": {
        "current_iso": "{{ iso8601 }}",
        "current_unix": {{ unix_timestamp }},
        "custom_format": "{{ \"iso8601\" | now: \"utc\" }}",
        "local_time": "{{ \"iso8601\" | now: \"local\" }}",
        "with_offset": "{{ 3600 | timestamp: \"unix\" }}",
        "negative_offset": "{{ -7200 | timestamp: \"unix\" }}"
      },
      "historical_data": {
        "past_week": "{{ 7 | past_date }}",
        "past_month": "{{ 30 | past_date }}",
        "past_year": "{{ 365 | past_date }}",
        "random_past": "{{ past_date }}"
      },
      "future_planning": {
        "next_week": "{{ 7 | future_date }}",
        "next_month": "{{ 30 | future_date }}",
        "next_year": "{{ 365 | future_date }}",
        "random_future": "{{ future_date }}"
      },
      "metadata": {
        "generated_at": "{{ iso8601 }}",
        "timezone": "{{ timezone }}"
      }
    }
    """
  end

  defp iot_sensors_comprehensive_example do
    """
    {
      "device_info": {
        "device_id": "{{ device_id }}",
        "custom_device": "{{ \"sensor\" | device_id: 3 }}",
        "status": "{{ device_status }}",
        "firmware": "{{ firmware_version }}",
        "uptime_hours": {{ 1 | uptime }},
        "uptime_days": {{ 24 | uptime }}
      },
      "environmental_sensors": {
        "temperature": {
          "celsius": {{ temperature }},
          "custom_range": {{ 15 | temperature: 30 }},
          "fahrenheit": {{ 68 | temperature: 86, "fahrenheit" }},
          "kelvin": {{ 273 | temperature: 303, "kelvin" }}
        },
        "humidity": {
          "percentage": {{ humidity }},
          "low_range": {{ 0 | humidity: 40 }},
          "high_range": {{ 60 | humidity: 100 }}
        },
        "pressure": {
          "hpa": {{ pressure }},
          "custom_range": {{ 100 | pressure: 1024 }},
          "low_pressure": {{ 950 | pressure: 1000 }}
        },
        "air_quality": {
          "aqi": {{ air_quality }},
          "good_range": {{ 0 | air_quality: 50 }},
          "moderate_range": {{ 51 | air_quality: 100 }},
          "unhealthy_range": {{ 151 | air_quality: 300 }}
        },
        "light_level": {
          "lux": {{ light_level }},
          "indoor": {{ 0 | light_level: 500 }},
          "outdoor": {{ 500 | light_level: 1000 }}
        }
      },
      "device_status": {
        "battery_level": {{ battery_level }},
        "signal_strength": {{ signal_strength }},
        "weak_signal": {{ -90 | signal_strength: -70 }},
        "strong_signal": {{ -60 | signal_strength: -30 }}
      },
      "timestamp": "{{ iso8601 }}"
    }
    """
  end

  defp person_data_comprehensive_example do
    """
    {
      "user_profile": {
        "full_name": "{{ name }}",
        "first_name": "{{ first_name }}",
        "last_name": "{{ last_name }}",
        "title": "{{ title }}",
        "email": "{{ email }}",
        "username": "{{ username }}",
        "date_of_birth": "{{ date_of_birth }}"
      },
      "contact_info": {
        "primary_email": "{{ email }}",
        "work_email": "{{ email }}",
        "personal_email": "{{ email }}"
      },
      "profile_metadata": {
        "created_at": "{{ iso8601 }}",
        "user_id": "{{ uuid }}",
        "is_active": {{ random_bool }}
      }
    }
    """
  end

  defp address_location_comprehensive_example do
    """
    {
      "location_data": {
        "address": "{{ address }}",
        "city": "{{ city }}",
        "state": "{{ state }}",
        "country": "{{ country }}",
        "country_code": "{{ country_code }}",
        "postcode": "{{ postcode }}",
        "timezone": "{{ timezone }}"
      },
      "coordinates": {
        "latitude": {{ latitude }},
        "longitude": {{ longitude }},
        "geohash": "{{ geohash }}"
      },
      "multiple_locations": [
        {
          "type": "home",
          "city": "{{ city }}",
          "country": "{{ country }}"
        },
        {
          "type": "work",
          "city": "{{ city }}",
          "country": "{{ country }}"
        }
      ],
      "metadata": {
        "generated_at": "{{ iso8601 }}"
      }
    }
    """
  end

  defp internet_tech_comprehensive_example do
    """
    {
      "network_info": {
        "domain": "{{ domain }}",
        "url": "{{ url }}",
        "ipv4_address": "{{ ipv4 }}",
        "ipv6_address": "{{ ipv6 }}",
        "mac_address": "{{ mac_address }}"
      },
      "browser_info": {
        "user_agent": "{{ user_agent }}"
      },
      "multiple_endpoints": [
        {
          "service": "api",
          "domain": "{{ domain }}",
          "ip": "{{ ipv4 }}"
        },
        {
          "service": "cdn",
          "domain": "{{ domain }}",
          "ip": "{{ ipv4 }}"
        }
      ],
      "metadata": {
        "generated_at": "{{ iso8601 }}"
      }
    }
    """
  end

  defp company_business_comprehensive_example do
    """
    {
      "company_info": {
        "name": "{{ company }}",
        "suffix": "{{ company_suffix }}",
        "full_name": "{{ company }} {{ company_suffix }}",
        "department": "{{ department }}"
      },
      "marketing": {
        "buzzword": "{{ buzzword }}",
        "catch_phrase": "{{ catch_phrase }}"
      },
      "departments": [
        "{{ department }}",
        "{{ department }}",
        "{{ department }}"
      ],
      "metadata": {
        "created_at": "{{ iso8601 }}",
        "company_id": "{{ uuid }}"
      }
    }
    """
  end

  defp commerce_products_comprehensive_example do
    """
    {
      "product_catalog": {
        "product_name": "{{ product_name }}",
        "price": "{{ price }}",
        "category": "electronics"
      },
      "vehicle_inventory": {
        "vehicle": "{{ vehicle }}",
        "make": "{{ vehicle_make }}",
        "model": "{{ vehicle_model }}",
        "vin": "{{ vin }}",
        "year": {{ 2020 | random_int: 2024 }}
      },
      "multiple_products": [
        {
          "name": "{{ product_name }}",
          "price": "{{ price }}"
        },
        {
          "name": "{{ product_name }}",
          "price": "{{ price }}"
        }
      ],
      "metadata": {
        "catalog_id": "{{ uuid }}",
        "updated_at": "{{ iso8601 }}"
      }
    }
    """
  end

  defp text_content_comprehensive_example do
    """
    {
      "content": {
        "word": "{{ word }}",
        "sentence": "{{ sentence }}",
        "paragraph": "{{ paragraph }}",
        "multiple_words": "{{ 5 | words }}"
      },
      "markdown_content": {
        "basic": "{{ markdown }}",
        "headers": "{{ markdown_headers }}",
        "emphasis": "{{ markdown_emphasis }}",
        "inline_code": "{{ markdown_inline_code }}",
        "block_code": "{{ markdown_block_code }}",
        "ordered_list": "{{ markdown_ordered_list }}",
        "unordered_list": "{{ markdown_unordered_list }}",
        "table": "{{ markdown_table }}"
      },
      "metadata": {
        "content_id": "{{ uuid }}",
        "generated_at": "{{ iso8601 }}"
      }
    }
    """
  end

  defp financial_currency_comprehensive_example do
    """
    {
      "currency_info": {
        "code": "{{ currency_code }}",
        "symbol": "{{ currency_symbol }}"
      },
      "banking": {
        "iban": "{{ iban }}"
      },
      "cryptocurrency": {
        "bitcoin_address": "{{ bitcoin_address }}",
        "ethereum_address": "{{ ethereum_address }}",
        "ethereum_signature": "{{ ethereum_signature }}"
      },
      "transaction": {
        "amount": "{{ price }}",
        "currency": "{{ currency_code }}",
        "transaction_id": "{{ uuid }}"
      },
      "metadata": {
        "generated_at": "{{ iso8601 }}"
      }
    }
    """
  end

  defp food_ingredients_comprehensive_example do
    """
    {
      "menu_item": {
        "dish": "{{ dish }}",
        "main_ingredient": "{{ ingredient }}",
        "spice": "{{ spice }}"
      },
      "recipe": {
        "ingredients": [
          "{{ ingredient }}",
          "{{ ingredient }}",
          "{{ ingredient }}"
        ],
        "spices": [
          "{{ spice }}",
          "{{ spice }}"
        ]
      },
      "restaurant_menu": [
        {
          "name": "{{ dish }}",
          "price": "{{ price }}"
        },
        {
          "name": "{{ dish }}",
          "price": "{{ price }}"
        }
      ],
      "metadata": {
        "menu_id": "{{ uuid }}",
        "updated_at": "{{ iso8601 }}"
      }
    }
    """
  end

  defp files_media_comprehensive_example do
    """
    {
      "file_info": {
        "filename": "{{ filename }}",
        "extension": "{{ file_extension }}",
        "mime_type": "{{ mime_type }}"
      },
      "media_urls": {
        "avatar": "{{ avatar_url }}",
        "image": "{{ image_url }}"
      },
      "file_collection": [
        {
          "name": "{{ filename }}",
          "type": "{{ mime_type }}"
        },
        {
          "name": "{{ filename }}",
          "type": "{{ mime_type }}"
        }
      ],
      "metadata": {
        "collection_id": "{{ uuid }}",
        "created_at": "{{ iso8601 }}"
      }
    }
    """
  end

  defp encoding_decoding_comprehensive_example do
    """
    {
      "original_data": {
        "text": "Hello World",
        "json_object": {"key": "value", "number": 42},
        "url_text": "hello world with spaces"
      },
      "base64_operations": {
        "encoded": "{{ \"Hello World\" | base64_encode }}",
        "decoded": "{{ \"SGVsbG8gV29ybGQ=\" | base64_decode }}"
      },
      "json_operations": {
        "encoded": "{{ {\"key\": \"value\"} | json_encode }}",
        "decoded": "{{ \"{\\\"key\\\":\\\"value\\\"}\" | json_decode }}"
      },
      "url_operations": {
        "encoded": "{{ \"hello world\" | url_encode }}",
        "decoded": "{{ \"hello%20world\" | url_decode }}"
      },
      "hashing": {
        "md5": "{{ \"Hello World\" | hash: \"md5\" }}",
        "sha1": "{{ \"Hello World\" | hash: \"sha1\" }}",
        "sha256": "{{ \"Hello World\" | hash: \"sha256\" }}"
      },
      "metadata": {
        "processed_at": "{{ iso8601 }}"
      }
    }
    """
  end

  defp math_functions_comprehensive_example do
    """
    {
      "basic_operations": {
        "original_float": 3.14159,
        "rounded": {{ 3.14159 | round: 2 }},
        "ceiling": {{ 3.14 | ceil }},
        "floor": {{ 3.99 | floor }},
        "absolute": {{ -5.5 | abs }}
      },
      "comparisons": {
        "minimum": {{ 10 | min: 5 }},
        "maximum": {{ 10 | max: 15 }},
        "clamped_low": {{ 2 | clamp: 5, 10 }},
        "clamped_high": {{ 15 | clamp: 5, 10 }},
        "clamped_normal": {{ 7 | clamp: 5, 10 }}
      },
      "random_calculations": {
        "random_value": {{ random_float }},
        "rounded_random": {{ random_float | round: 3 }},
        "clamped_random": {{ random_float | clamp: 0.1, 0.9 }}
      },
      "metadata": {
        "calculated_at": "{{ iso8601 }}"
      }
    }
    """
  end

  defp string_manipulation_comprehensive_example do
    """
    {
      "original_strings": {
        "sample_text": "hello world",
        "mixed_case": "HeLLo WoRLd",
        "long_text": "This is a very long string that will be truncated"
      },
      "case_transformations": {
        "capitalized": "{{ \"hello world\" | capitalize }}",
        "uppercase": "{{ \"hello world\" | uppercase }}",
        "lowercase": "{{ \"HELLO WORLD\" | lowercase }}"
      },
      "text_manipulation": {
        "truncated_short": "{{ \"hello world\" | truncate: 5 }}",
        "truncated_long": "{{ \"This is a very long string\" | truncate: 10 }}",
        "padded_left": "{{ \"hello\" | pad_left: 10, \"0\" }}",
        "padded_right": "{{ \"hello\" | pad_right: 10, \"-\" }}",
        "padded_spaces": "{{ \"test\" | pad_left: 8, \" \" }}"
      },
      "combined_operations": {
        "processed": "{{ \"hello world\" | capitalize | pad_right: 15, \".\" }}"
      },
      "metadata": {
        "processed_at": "{{ iso8601 }}"
      }
    }
    """
  end

  defp date_processing_comprehensive_example do
    """
    {
      "current_time": "{{ iso8601 }}",
      "time_calculations": {
        "plus_one_hour": "{{ 3600 | date_add }}",
        "plus_one_day": "{{ 86400 | date_add }}",
        "minus_one_hour": "{{ -3600 | date_add }}"
      },
      "date_formatting": {
        "iso_format": "{{ date_format: \"%Y-%m-%d\" }}",
        "us_format": "{{ date_format: \"%m/%d/%Y\" }}",
        "time_only": "{{ date_format: \"%H:%M:%S\" }}",
        "full_format": "{{ date_format: \"%Y-%m-%d %H:%M:%S\" }}"
      },
      "timezone_conversions": {
        "utc": "{{ now | timezone_convert: \"UTC\" }}",
        "new_york": "{{ now | timezone_convert: \"America/New_York\" }}",
        "london": "{{ now | timezone_convert: \"Europe/London\" }}",
        "tokyo": "{{ now | timezone_convert: \"Asia/Tokyo\" }}"
      },
      "metadata": {
        "generated_at": "{{ iso8601 }}",
        "timezone": "{{ timezone }}"
      }
    }
    """
  end

  defp get_broker_variables(broker_name) when is_binary(broker_name) and broker_name != "" do
    # Get all connection sets
    connection_sets = Mqttable.ConnectionSets.get_all()

    # Find the broker by name
    broker =
      Enum.find(connection_sets, fn set ->
        Map.get(set, :name) == broker_name
      end)

    case broker do
      nil ->
        %{}

      broker ->
        # Extract enabled variables and convert to map
        broker
        |> Map.get(:variables, [])
        |> Enum.filter(fn var -> Map.get(var, :enabled, true) end)
        |> Enum.reduce(%{}, fn var, acc ->
          name = Map.get(var, :name)
          value = Map.get(var, :value, "")

          if name && name != "" do
            Map.put(acc, name, value)
          else
            acc
          end
        end)
    end
  end

  defp get_broker_variables(_broker_name) do
    %{}
  end
end
